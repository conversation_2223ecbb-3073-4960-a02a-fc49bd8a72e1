// Dashboard functionality
class SpotifyDashboard {
    constructor() {
        this.baseUrl = 'https://spotifywidget.ilikepancakes.ink';
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAuthStatus();
        this.updateWidgetPreview();
    }

    bindEvents() {
        // Connect button
        document.getElementById('connectBtn').addEventListener('click', () => {
            this.connectSpotify();
        });

        // Disconnect button
        document.getElementById('disconnectBtn').addEventListener('click', () => {
            this.disconnectSpotify();
        });

        // Widget option changes
        ['widgetTheme', 'widgetSize', 'showControls', 'showProgress'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => {
                this.updateWidgetPreview();
                this.generateEmbedCode();
            });
        });

        // Copy embed code
        document.getElementById('copyEmbedBtn').addEventListener('click', () => {
            this.copyEmbedCode();
        });
    }

    connectSpotify() {
        const userId = document.getElementById('userId').value.trim() || 'default';
        const authUrl = `${this.baseUrl}/api/auth?userId=${encodeURIComponent(userId)}`;
        window.location.href = authUrl;
    }

    disconnectSpotify() {
        localStorage.removeItem('spotify_user_id');
        localStorage.removeItem('spotify_refresh_token');
        this.showAuthSection();
    }

    checkAuthStatus() {
        // Check URL parameters for auth callback
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('access_token')) {
            const userId = urlParams.get('userId') || 'default';
            const refreshToken = urlParams.get('refresh_token');
            
            if (refreshToken) {
                localStorage.setItem('spotify_user_id', userId);
                localStorage.setItem('spotify_refresh_token', refreshToken);
                
                // Clean URL
                window.history.replaceState({}, document.title, window.location.pathname);
                
                this.showConnectedSection(userId);
                return;
            }
        }

        // Check localStorage for existing auth
        const storedUserId = localStorage.getItem('spotify_user_id');
        if (storedUserId) {
            this.showConnectedSection(storedUserId);
        } else {
            this.showAuthSection();
        }
    }

    showAuthSection() {
        document.getElementById('authSection').style.display = 'block';
        document.getElementById('connectedSection').style.display = 'none';
        document.getElementById('widgetSection').style.display = 'none';
    }

    showConnectedSection(userId) {
        document.getElementById('authSection').style.display = 'none';
        document.getElementById('connectedSection').style.display = 'block';
        document.getElementById('widgetSection').style.display = 'block';
        
        document.getElementById('connectedUserId').textContent = userId;
        this.generateEmbedCode();
    }

    updateWidgetPreview() {
        const userId = localStorage.getItem('spotify_user_id') || 'default';
        const theme = document.getElementById('widgetTheme').value;
        const size = document.getElementById('widgetSize').value;
        const showControls = document.getElementById('showControls').checked;
        const showProgress = document.getElementById('showProgress').checked;

        const params = new URLSearchParams({
            userId,
            theme,
            size,
            controls: showControls,
            progress: showProgress
        });

        const widgetUrl = `${this.baseUrl}/widget.html?${params.toString()}`;
        document.getElementById('widgetPreview').src = widgetUrl;

        // Update preview size based on selection
        const preview = document.getElementById('widgetPreview');
        switch(size) {
            case 'small':
                preview.width = '300';
                preview.height = '150';
                break;
            case 'medium':
                preview.width = '400';
                preview.height = '200';
                break;
            case 'large':
                preview.width = '500';
                preview.height = '250';
                break;
        }
    }

    generateEmbedCode() {
        const userId = localStorage.getItem('spotify_user_id') || 'default';
        const theme = document.getElementById('widgetTheme').value;
        const size = document.getElementById('widgetSize').value;
        const showControls = document.getElementById('showControls').checked;
        const showProgress = document.getElementById('showProgress').checked;

        const params = new URLSearchParams({
            userId,
            theme,
            size,
            controls: showControls,
            progress: showProgress
        });

        const widgetUrl = `${this.baseUrl}/widget.html?${params.toString()}`;
        
        let width, height;
        switch(size) {
            case 'small':
                width = '300';
                height = '150';
                break;
            case 'medium':
                width = '400';
                height = '200';
                break;
            case 'large':
                width = '500';
                height = '250';
                break;
        }

        const embedCode = `<iframe src="${widgetUrl}" width="${width}" height="${height}" frameborder="0" style="border-radius: 15px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"></iframe>`;
        
        document.getElementById('embedCode').value = embedCode;
    }

    async copyEmbedCode() {
        const embedCode = document.getElementById('embedCode').value;
        try {
            await navigator.clipboard.writeText(embedCode);
            
            const btn = document.getElementById('copyEmbedBtn');
            const originalText = btn.textContent;
            btn.textContent = 'Copied!';
            btn.style.background = '#1db954';
            btn.style.color = 'white';
            
            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = '';
                btn.style.color = '';
            }, 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
            alert('Failed to copy embed code. Please copy manually.');
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SpotifyDashboard();
});
