// Spotify OAuth handler for dashboard authentication
export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const { SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET } = process.env;
  
  if (!SPOTIFY_CLIENT_ID || !SPOTIFY_CLIENT_SECRET) {
    return res.status(500).json({ error: 'Missing Spotify app credentials' });
  }

  if (req.method === 'GET') {
    // Step 1: Redirect to Spotify authorization
    const scopes = [
      'user-read-currently-playing',
      'user-read-recently-played',
      'user-read-playback-state',
      'user-modify-playback-state'
    ].join(' ');

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: SPOTIFY_CLIENT_ID,
      scope: scopes,
      redirect_uri: `${req.headers.origin || 'http://localhost:3000'}/api/auth`,
      state: req.query.userId || 'default'
    });

    if (req.query.code) {
      // Step 2: Exchange code for tokens
      try {
        const tokenResponse = await fetch('https://accounts.spotify.com/api/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${Buffer.from(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`).toString('base64')}`
          },
          body: new URLSearchParams({
            grant_type: 'authorization_code',
            code: req.query.code,
            redirect_uri: `${req.headers.origin || 'http://localhost:3000'}/api/auth`
          })
        });

        if (!tokenResponse.ok) {
          throw new Error('Failed to exchange code for tokens');
        }

        const tokenData = await tokenResponse.json();
        const userId = req.query.state || 'default';

        // In a real app, you'd save these to a database
        // For demo purposes, we'll return them to be stored client-side
        res.status(200).json({
          success: true,
          userId: userId,
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token,
          expires_in: tokenData.expires_in,
          message: 'Authentication successful! Save your refresh token securely.'
        });

      } catch (error) {
        console.error('Token exchange error:', error);
        res.status(500).json({ error: 'Failed to authenticate with Spotify' });
      }
    } else {
      // Redirect to Spotify authorization
      res.redirect(`https://accounts.spotify.com/authorize?${params.toString()}`);
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
}
