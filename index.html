<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spotify Widget Dashboard</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <h1>🎵 Spotify Widget Dashboard</h1>
            <p>Connect your Spotify account and generate embeddable widgets</p>
        </header>

        <main class="dashboard-main">
            <!-- Authentication Section -->
            <section class="auth-section" id="authSection">
                <div class="card">
                    <h2>Connect Spotify Account</h2>
                    <div class="auth-form">
                        <div class="form-group">
                            <label for="userId">User ID (for multiple accounts):</label>
                            <input type="text" id="userId" placeholder="Enter a unique user ID" value="default">
                        </div>
                        <button class="btn btn-primary" id="connectBtn">
                            <svg class="spotify-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.84-.179-.84-.66 0-.359.24-.66.54-.78 4.56-1.021 8.52-.6 11.64 **********.479.659.242 1.021zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.************* 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
                            </svg>
                            Connect with Spotify
                        </button>
                    </div>
                </div>
            </section>

            <!-- Connected Account Section -->
            <section class="connected-section" id="connectedSection" style="display: none;">
                <div class="card">
                    <h2>✅ Account Connected</h2>
                    <div class="account-info">
                        <p><strong>User ID:</strong> <span id="connectedUserId"></span></p>
                        <p><strong>Status:</strong> <span class="status-connected">Connected</span></p>
                    </div>
                    <button class="btn btn-secondary" id="disconnectBtn">Disconnect</button>
                </div>
            </section>

            <!-- Widget Generator Section -->
            <section class="widget-section" id="widgetSection" style="display: none;">
                <div class="card">
                    <h2>Generate Widget</h2>
                    <div class="widget-options">
                        <div class="form-group">
                            <label for="widgetTheme">Theme:</label>
                            <select id="widgetTheme">
                                <option value="acrylic">Acrylic (Default)</option>
                                <option value="dark">Dark</option>
                                <option value="light">Light</option>
                                <option value="minimal">Minimal</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="widgetSize">Size:</label>
                            <select id="widgetSize">
                                <option value="small">Small (300x150)</option>
                                <option value="medium">Medium (400x200)</option>
                                <option value="large">Large (500x250)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="showControls"> Show playback controls
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="showProgress"> Show progress bar
                            </label>
                        </div>
                    </div>

                    <div class="widget-preview">
                        <h3>Preview:</h3>
                        <iframe id="widgetPreview" src="" width="400" height="200" frameborder="0"></iframe>
                    </div>

                    <div class="embed-code">
                        <h3>Embed Code:</h3>
                        <textarea id="embedCode" readonly></textarea>
                        <button class="btn btn-secondary" id="copyEmbedBtn">Copy to Clipboard</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
