<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spotify Now Playing Widget</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="widget-container">
        <div class="now-playing-widget" id="nowPlayingWidget">
            <!-- Loading state -->
            <div class="loading-state" id="loadingState">
                <div class="loading-spinner"></div>
                <p>Loading...</p>
            </div>

            <!-- Main content -->
            <div class="widget-content" id="widgetContent" style="display: none;">
                <!-- Status indicator -->
                <div class="status-indicator" id="statusIndicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span class="status-text" id="statusText">Now Playing</span>
                </div>

                <!-- Track info -->
                <div class="track-info">
                    <div class="album-art-container">
                        <img class="album-art" id="albumArt" src="" alt="Album Art">
                        <div class="play-overlay" id="playOverlay">
                            <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="track-details">
                        <h3 class="track-title" id="trackTitle">Track Title</h3>
                        <p class="track-artist" id="trackArtist">Artist Name</p>
                        <p class="track-album" id="trackAlbum">Album Name</p>
                        
                        <!-- Progress bar (only for currently playing) -->
                        <div class="progress-container" id="progressContainer" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="progress-time">
                                <span id="currentTime">0:00</span>
                                <span id="totalTime">0:00</span>
                            </div>
                        </div>

                        <!-- Timestamp for recent tracks -->
                        <div class="timestamp" id="timestamp" style="display: none;">
                            <span id="timestampText"></span>
                        </div>
                    </div>
                </div>

                <!-- Optional playback controls -->
                <div class="playback-controls" id="playbackControls" style="display: none;">
                    <button class="control-btn" id="prevBtn">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                        </svg>
                    </button>
                    <button class="control-btn play-pause-btn" id="playPauseBtn">
                        <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                        </svg>
                    </button>
                    <button class="control-btn" id="nextBtn">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                        </svg>
                    </button>
                </div>

                <!-- Spotify link -->
                <a class="spotify-link" id="spotifyLink" href="#" target="_blank" rel="noopener noreferrer">
                    <svg class="spotify-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.84-.179-.84-.66 0-.359.24-.66.54-.78 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.242 1.021zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
                    </svg>
                    Open in Spotify
                </a>
            </div>

            <!-- Error state -->
            <div class="error-state" id="errorState" style="display: none;">
                <p>Unable to load Spotify data</p>
                <button class="retry-btn" id="retryBtn">Retry</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
