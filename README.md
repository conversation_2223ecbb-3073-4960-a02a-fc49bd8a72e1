# Spotify Now Playing Widget

A beautiful, embeddable Spotify "Now Playing" widget with acrylic design, hosted on Vercel at `spotifywidget.ilikepancakes.ink`.

## Features

- 🎵 **Real-time Now Playing**: Shows current Spotify track with live updates
- ⏸️ **Smart Fallback**: Falls back to last played track when music is paused
- 🕐 **JST Timestamps**: Displays last played time in Japan Standard Time
- 🎨 **Acrylic Theme**: Clean, modern glassmorphism design
- 📱 **Responsive**: Works on all screen sizes
- ⚙️ **Configurable**: Multiple themes, sizes, and optional controls
- 🔗 **Easy Embedding**: Simple iframe embed code generation

## Setup Instructions

### 1. Create Spotify App

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app
3. Note your `Client ID` and `Client Secret`
4. Add redirect URI: `https://spotifywidget.ilikepancakes.ink/api/auth`

### 2. Configure Environment Variables

In your Vercel dashboard, add these environment variables:

```
SPOTIFY_CLIENT_ID=your_client_id_here
SPOTIFY_CLIENT_SECRET=your_client_secret_here
```

### 3. Connect Your Account

1. Visit `https://spotifywidget.ilikepancakes.ink`
2. Enter a unique User ID (or use "default")
3. Click "Connect with Spotify"
4. Authorize the application
5. Save your refresh token (displayed after auth)

### 4. Add User-Specific Token

Add an environment variable for your user:
```
SPOTIFY_REFRESH_TOKEN_youruserid=your_refresh_token_here
```

Or for the default user:
```
SPOTIFY_REFRESH_TOKEN=your_refresh_token_here
```

## Usage

### Dashboard
Visit the main site to:
- Connect your Spotify account
- Configure widget appearance
- Generate embed codes
- Preview your widget

### Widget Options

- **Theme**: Acrylic (default), Dark, Light, Minimal
- **Size**: Small (300x150), Medium (400x200), Large (500x250)
- **Controls**: Optional playback controls
- **Progress**: Show progress bar for current tracks

### Embedding

Use the generated iframe code:

```html
<iframe 
  src="https://spotifywidget.ilikepancakes.ink/widget.html?userId=default&theme=acrylic&size=medium&controls=false&progress=true" 
  width="400" 
  height="200" 
  frameborder="0" 
  style="border-radius: 15px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
</iframe>
```

## API Endpoints

### `/api/auth`
Handles Spotify OAuth flow
- **GET**: Redirects to Spotify authorization
- **GET** (with code): Exchanges code for tokens

### `/api/spotify`
Fetches current/recent track data
- **Query**: `userId` - User identifier
- **Returns**: Current track, recent track, playing status, timestamp

## File Structure

```
├── index.html          # Dashboard page
├── widget.html         # Embeddable widget
├── dashboard.css       # Dashboard styles
├── dashboard.js        # Dashboard functionality
├── widget.css          # Widget styles (acrylic theme)
├── widget.js           # Widget functionality
├── api/
│   ├── auth.js         # Spotify OAuth handler
│   └── spotify.js      # Spotify API handler
├── vercel.json         # Vercel configuration
└── README.md           # This file
```

## Themes

### Acrylic (Default)
- Glassmorphism effect with backdrop blur
- Semi-transparent background
- Smooth animations

### Dark
- Dark background with white text
- Suitable for dark websites

### Light
- Light background with dark text
- Clean, minimal appearance

### Minimal
- Simplified design without blur effects
- Lightweight and fast

## Browser Support

- Chrome/Edge 76+
- Firefox 103+
- Safari 16.4+
- Mobile browsers with backdrop-filter support

## Troubleshooting

### Widget shows "Unable to load Spotify data"
1. Check environment variables are set correctly
2. Verify user has completed OAuth flow
3. Ensure refresh token is valid
4. Check Spotify app permissions

### Authentication fails
1. Verify redirect URI in Spotify app settings
2. Check client ID/secret are correct
3. Ensure user grants all required permissions

### Widget appears blank
1. Check iframe src URL is correct
2. Verify user ID matches authenticated user
3. Check browser console for errors

## Development

To run locally:
1. Clone repository
2. Set up environment variables
3. Deploy to Vercel or run with Vercel CLI
4. Configure Spotify app redirect URI for your domain

## License

MIT License - feel free to use and modify for your projects.
