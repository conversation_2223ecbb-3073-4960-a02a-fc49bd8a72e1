/* Spotify Widget Styles - Acrylic Theme */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: transparent;
    overflow: hidden;
}

.widget-container {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
}

.now-playing-widget {
    width: 100%;
    max-width: 500px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #333;
    position: relative;
    overflow: hidden;
}

.now-playing-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 2rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255,255,255,0.3);
    border-top: 3px solid #1db954;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicator */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #1db954;
    animation: pulse 2s infinite;
}

.status-dot.paused {
    background: #ffa500;
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Track Info */
.track-info {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.album-art-container {
    position: relative;
    flex-shrink: 0;
}

.album-art {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.album-art-container:hover .play-overlay {
    opacity: 1;
}

.play-icon {
    width: 16px;
    height: 16px;
    color: white;
    margin-left: 2px;
}

.track-details {
    flex: 1;
    min-width: 0;
}

.track-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #333;
}

.track-artist {
    font-size: 0.95rem;
    color: #666;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.track-album {
    font-size: 0.85rem;
    color: #888;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Progress Bar */
.progress-container {
    margin-top: 10px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: #1db954;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-time {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #666;
}

/* Timestamp */
.timestamp {
    margin-top: 8px;
    font-size: 0.8rem;
    color: #666;
}

/* Playback Controls */
.playback-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 15px 0;
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.control-btn svg {
    width: 20px;
    height: 20px;
}

.play-pause-btn {
    width: 50px;
    height: 50px;
    background: #1db954;
    color: white;
}

.play-pause-btn:hover {
    background: #1ed760;
}

/* Spotify Link */
.spotify-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(29, 185, 84, 0.1);
    border: 1px solid rgba(29, 185, 84, 0.3);
    border-radius: 20px;
    color: #1db954;
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.spotify-link:hover {
    background: rgba(29, 185, 84, 0.2);
    transform: translateY(-1px);
}

.spotify-link .spotify-icon {
    width: 16px;
    height: 16px;
}

/* Error State */
.error-state {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.retry-btn {
    margin-top: 1rem;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Theme Variations */
.widget-container[data-theme="dark"] .now-playing-widget {
    background: rgba(0, 0, 0, 0.3);
    color: white;
}

.widget-container[data-theme="dark"] .track-title {
    color: white;
}

.widget-container[data-theme="dark"] .track-artist {
    color: #ccc;
}

.widget-container[data-theme="dark"] .track-album {
    color: #999;
}

.widget-container[data-theme="light"] .now-playing-widget {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.widget-container[data-theme="minimal"] .now-playing-widget {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Size Variations */
.widget-container[data-size="small"] .now-playing-widget {
    padding: 15px;
}

.widget-container[data-size="small"] .album-art {
    width: 60px;
    height: 60px;
}

.widget-container[data-size="small"] .track-title {
    font-size: 1rem;
}

.widget-container[data-size="large"] .now-playing-widget {
    padding: 25px;
}

.widget-container[data-size="large"] .album-art {
    width: 100px;
    height: 100px;
}

.widget-container[data-size="large"] .track-title {
    font-size: 1.3rem;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .track-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .album-art {
        width: 120px !important;
        height: 120px !important;
    }
}
