// Spotify Widget JavaScript
class SpotifyWidget {
    constructor() {
        this.baseUrl = 'https://spotifywidget.ilikepancakes.ink';
        this.updateInterval = null;
        this.progressInterval = null;
        this.currentTrack = null;
        this.isPlaying = false;
        
        this.init();
    }

    init() {
        this.parseUrlParams();
        this.applyTheme();
        this.bindEvents();
        this.startUpdating();
    }

    parseUrlParams() {
        const params = new URLSearchParams(window.location.search);
        this.userId = params.get('userId') || 'default';
        this.theme = params.get('theme') || 'acrylic';
        this.size = params.get('size') || 'medium';
        this.showControls = params.get('controls') === 'true';
        this.showProgress = params.get('progress') === 'true';
    }

    applyTheme() {
        const container = document.querySelector('.widget-container');
        container.setAttribute('data-theme', this.theme);
        container.setAttribute('data-size', this.size);

        // Show/hide optional elements
        if (this.showControls) {
            document.getElementById('playbackControls').style.display = 'flex';
        }
    }

    bindEvents() {
        // Retry button
        document.getElementById('retryBtn').addEventListener('click', () => {
            this.fetchSpotifyData();
        });

        // Album art click
        document.getElementById('albumArt').addEventListener('click', () => {
            if (this.currentTrack && this.currentTrack.external_url) {
                window.open(this.currentTrack.external_url, '_blank');
            }
        });

        // Spotify link
        document.getElementById('spotifyLink').addEventListener('click', (e) => {
            e.preventDefault();
            if (this.currentTrack && this.currentTrack.external_url) {
                window.open(this.currentTrack.external_url, '_blank');
            }
        });
    }

    startUpdating() {
        this.fetchSpotifyData();
        // Update every 30 seconds
        this.updateInterval = setInterval(() => {
            this.fetchSpotifyData();
        }, 30000);
    }

    async fetchSpotifyData() {
        try {
            const response = await fetch(`${this.baseUrl}/api/spotify?userId=${encodeURIComponent(this.userId)}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            this.updateWidget(data);
            this.showContent();

        } catch (error) {
            console.error('Failed to fetch Spotify data:', error);
            this.showError();
        }
    }

    updateWidget(data) {
        const track = data.current || data.recent;
        this.currentTrack = track;
        this.isPlaying = data.is_playing;

        if (!track) {
            this.showError();
            return;
        }

        // Update status
        this.updateStatus(data.is_playing, data.current ? 'current' : 'recent');

        // Update track info
        document.getElementById('trackTitle').textContent = track.name;
        document.getElementById('trackArtist').textContent = track.artist;
        document.getElementById('trackAlbum').textContent = track.album;
        
        // Update album art
        const albumArt = document.getElementById('albumArt');
        if (track.image) {
            albumArt.src = track.image;
            albumArt.style.display = 'block';
        } else {
            albumArt.style.display = 'none';
        }

        // Update progress bar for current track
        if (data.current && data.is_playing && this.showProgress) {
            this.updateProgress(data.current.progress_ms, data.current.duration_ms);
            document.getElementById('progressContainer').style.display = 'block';
        } else {
            document.getElementById('progressContainer').style.display = 'none';
        }

        // Update timestamp for recent tracks
        if (data.recent && !data.current) {
            this.updateTimestamp(data.recent.played_at);
            document.getElementById('timestamp').style.display = 'block';
        } else {
            document.getElementById('timestamp').style.display = 'none';
        }

        // Update Spotify link
        if (track.external_url) {
            document.getElementById('spotifyLink').href = track.external_url;
        }
    }

    updateStatus(isPlaying, trackType) {
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');

        if (trackType === 'current' && isPlaying) {
            statusDot.className = 'status-dot';
            statusText.textContent = 'Now Playing';
        } else if (trackType === 'current' && !isPlaying) {
            statusDot.className = 'status-dot paused';
            statusText.textContent = 'Paused';
        } else {
            statusDot.className = 'status-dot paused';
            statusText.textContent = 'Last Played';
        }
    }

    updateProgress(progressMs, durationMs) {
        const progressPercent = (progressMs / durationMs) * 100;
        document.getElementById('progressFill').style.width = `${progressPercent}%`;
        
        document.getElementById('currentTime').textContent = this.formatTime(progressMs);
        document.getElementById('totalTime').textContent = this.formatTime(durationMs);

        // Start progress animation if playing
        if (this.isPlaying) {
            this.startProgressAnimation(progressMs, durationMs);
        }
    }

    startProgressAnimation(startMs, durationMs) {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        let currentMs = startMs;
        this.progressInterval = setInterval(() => {
            currentMs += 1000; // Add 1 second
            
            if (currentMs >= durationMs) {
                clearInterval(this.progressInterval);
                return;
            }

            const progressPercent = (currentMs / durationMs) * 100;
            document.getElementById('progressFill').style.width = `${progressPercent}%`;
            document.getElementById('currentTime').textContent = this.formatTime(currentMs);
        }, 1000);
    }

    updateTimestamp(playedAt) {
        const playedDate = new Date(playedAt);
        const jstDate = new Date(playedDate.toLocaleString("en-US", {timeZone: "Asia/Tokyo"}));
        const now = new Date();
        const nowJst = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Tokyo"}));
        
        const diffMs = nowJst - jstDate;
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        let timeText;
        if (diffMinutes < 1) {
            timeText = 'Just now';
        } else if (diffMinutes < 60) {
            timeText = `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
        } else if (diffHours < 24) {
            timeText = `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
        } else if (diffDays < 7) {
            timeText = `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
        } else {
            timeText = jstDate.toLocaleDateString('en-US', {
                timeZone: 'Asia/Tokyo',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        document.getElementById('timestampText').textContent = `${timeText} (JST)`;
    }

    formatTime(ms) {
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    showContent() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('errorState').style.display = 'none';
        document.getElementById('widgetContent').style.display = 'block';
    }

    showError() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('widgetContent').style.display = 'none';
        document.getElementById('errorState').style.display = 'block';
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }
    }
}

// Initialize widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.spotifyWidget = new SpotifyWidget();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.spotifyWidget) {
        window.spotifyWidget.destroy();
    }
});
