// Spotify API handler for Vercel serverless function
export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const { SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET, SPOTIFY_REFRESH_TOKEN } = process.env;

    if (!SPOTIFY_CLIENT_ID || !SPOTIFY_CLIENT_SECRET || !SPOTIFY_REFRESH_TOKEN) {
      return res.status(500).json({ error: 'Missing Spotify credentials' });
    }

    // Get access token using refresh token
    const tokenResponse = await fetch('https://accounts.spotify.com/api/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`).toString('base64')}`
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: SPOTIFY_REFRESH_TOKEN
      })
    });

    if (!tokenResponse.ok) {
      throw new Error('Failed to refresh token');
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // Get currently playing track
    const currentlyPlayingResponse = await fetch('https://api.spotify.com/v1/me/player/currently-playing', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    let currentTrack = null;
    let isPlaying = false;

    if (currentlyPlayingResponse.ok && currentlyPlayingResponse.status !== 204) {
      const currentData = await currentlyPlayingResponse.json();
      if (currentData && currentData.item) {
        currentTrack = {
          name: currentData.item.name,
          artist: currentData.item.artists.map(artist => artist.name).join(', '),
          album: currentData.item.album.name,
          image: currentData.item.album.images[0]?.url,
          external_url: currentData.item.external_urls.spotify,
          progress_ms: currentData.progress_ms,
          duration_ms: currentData.item.duration_ms
        };
        isPlaying = currentData.is_playing;
      }
    }

    // If no current track or paused, get recently played
    let recentTrack = null;
    if (!currentTrack || !isPlaying) {
      const recentResponse = await fetch('https://api.spotify.com/v1/me/player/recently-played?limit=1', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (recentResponse.ok) {
        const recentData = await recentResponse.json();
        if (recentData.items && recentData.items.length > 0) {
          const track = recentData.items[0].track;
          recentTrack = {
            name: track.name,
            artist: track.artists.map(artist => artist.name).join(', '),
            album: track.album.name,
            image: track.album.images[0]?.url,
            external_url: track.external_urls.spotify,
            played_at: recentData.items[0].played_at
          };
        }
      }
    }

    // Return the data
    res.status(200).json({
      current: currentTrack,
      recent: recentTrack,
      is_playing: isPlaying,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Spotify API error:', error);
    res.status(500).json({ error: 'Failed to fetch Spotify data' });
  }
}
